# Changelog

## 2.0.1

### Added
- feat(Notification): support push notification if user never opened chat
- feat(Notifification): push notification with default sound
- chore(Log): show detail on console
- feat(Notifification): push notification with badge [iOS]

### Fixed
- fix(Notification): show undefined for title or body

## 2.0.0

### Added
- Add sample port for emulators to test functions
- Add guide to test function locally

### Changed
- Upgrade dependencies: `firebase-admin` and `firebase-functions`.
- Update `functions/index.js` to use `firebase-function` v2

### Removed 
- Unused `package.json` file.

## 1.0.0

### Added
- Initial project.
