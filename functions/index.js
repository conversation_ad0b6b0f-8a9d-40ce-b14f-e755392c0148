// The Cloud Functions for Firebase SDK to create Cloud Functions and triggers.
const { onDocumentCreated } = require("firebase-functions/v2/firestore");
const { onValueCreated } = require("firebase-functions/v2/database");

// The HTTP request handler.
const { onRequest } = require("firebase-functions/v2/https");

// The Firebase Admin SDK to access Firestore and Messaging.
const admin = require("firebase-admin");
admin.initializeApp();

// Create and deploy your first functions
// https://firebase.google.com/docs/functions/get-started

// Push Notification via HTTP request
// curl--location 'http://127.0.0.1:5001/fluxstore-inspireui/us-central1/pushNotification?email=test%40gmail.com&senderName=Chung%20Xon&message=test'
exports.pushNotification = onRequest((req, res) => {
  admin
    .firestore()
    .collection("users")
    .doc(req.query.email)
    .get()
    .then((snapshot) => {
      if (snapshot.empty) {
        res.status(400).json("Not Found");
      } else {
        const user = snapshot.data();
        const { senderName, message } = req.query;

        const messagePayload = {
          notification: {
            title: senderName
              ? `You have a message from "${senderName}"`
              : "You have a message",
            body: message ? message : null,
          },
          token: user.deviceToken,
          android: {
            priority: "high",
            notification: {
              sound: "default",
            },
          },
          apns: {
            headers: {
              "apns-priority": "10",
            },
            payload: {
              aps: {
                sound: "default",
                badge: 1,
              },
            },
          },
        };
        admin
          .messaging()
          .send(messagePayload)
          .then((response) => {
            console.log("Successfully sent message:", JSON.stringify(response));
            res.json({ success: true });
          })
          .catch((error) => {
            console.log("Error sending message:", JSON.stringify(error));
            res.status(500).send(error);
          });
      }
    })
    .catch((err) => {
      res.status(500).send(err);
    });
});

exports.sendNotification = onDocumentCreated(
  "chatRooms/{roomId}/chatScreen/{message}",
  (event) => {
    const snapshot = event.data;
    if (!snapshot) {
      console.log("No data associated with the event");
      return;
    }
    const { roomId } = event.params;
    const { text, sender } = snapshot.data();

    admin
      .firestore()
      .doc("chatRooms/" + roomId)
      .get()
      .then(async (snapshot) => {
        if (!snapshot.exists) {
          console.log("No data associated with the event");
          return;
        }
        const { users } = snapshot.data();

        if (users instanceof Array) {
          var receivers = [];

          for await (const user of users) {
            const { email, pushToken, unread, langCode } = user;
            if (email !== sender) {
              if (pushToken) {
                receivers.push({
                  token: pushToken,
                  // If `unread` is null or equal 0, set badge to 1
                  badge: unread ? unread : 1,
                });
              } else {
                const snapshot = await admin
                  .firestore()
                  .collection("users")
                  .doc(email)
                  .get();

                if (!snapshot.empty) {
                  const user = snapshot.data();
                  receivers.push({
                    token: user.deviceToken,
                    badge: unread ? unread : 1,
                  });
                }
              }
            }
          }

          if (receivers.empty) {
            console.log("No receivers found");
            return;
          }

          console.log("Receivers:", JSON.stringify(receivers));

          const message = {
            notification: {
              title: sender
                ? `You have a message from "${sender}"`
                : "You have a message",
              body: text ? text : null,
            },
            android: {
              priority: "high",
              notification: {
                sound: "default",
              },
            },
            apns: {
              headers: {
                "apns-priority": "10",
              },
              payload: {
                aps: {
                  sound: "default",
                  badge: 1,
                },
              },
            },
          };

          const messages = receivers.map((receiver) => {
            const { token, badge } = receiver;
            // deepcopy message
            var copy;
            if (global.structuredClone)
              // In some case, it trhow Error `ReferenceError:
              // structuredClone is not defined`. I dunno ^^
              copy = structuredClone(message);
            else copy = JSON.parse(JSON.stringify(message));
            // add token to copied message
            copy.token = token;
            // add badge to copied message
            copy.apns.payload.aps.badge = badge;
            return copy;
          });

          // console.log('Sending messages:', JSON.stringify(messages));

          admin
            .messaging()
            .sendEach(messages)
            .then((response) => {
              console.log(
                "Successfully sent message:",
                JSON.stringify(response)
              );
            })
            .catch((error) => {
              console.log("Error sending message:", JSON.stringify(error));
            });
        }
      });
  }
);

function buildNotifyMessageForLoyalty(transaction, token) {
  var message = {
    token: token,
    android: {
      priority: "high",
      notification: {
        sound: "default",
      },
    },
    apns: {
      headers: {
        "apns-priority": "10",
      },
      payload: {
        aps: {
          sound: "default",
          badge: 1,
        },
      },
    },
  };

  switch (transaction.type) {
    case "add":
      message = {
        ...message,
        notification: {
          title: "Points added successfully",
          body: `You’re earned ${transaction.points} points.`,
        },
        data: {
          type: "points_added",
        },
      };
      break;

    case "redeem":
      message = {
        ...message,
        notification: {
          title: "Points redeemed successfully",
          body: `You’re redeemed ${transaction.points} points.`,
        },
        data: {
          type: "points_redeemed",
        },
      };
      break;
    default:
      break;
  }

  return message;
}

exports.notifyOnLoyaltyTransactionCreated = onDocumentCreated(
  "loyalty_transactions/{transactionId}",
  async (event) => {
    const { transactionId } = event.params;
    const transactionSnapshot = await admin
      .firestore()
      .collection("loyalty_transactions")
      .doc(transactionId)
      .get();

    const userId = transactionSnapshot.data()?.user_id;
    const type = transactionSnapshot.data()?.type;

    if (type == "add" || type == "redeem") {
      const loyaltyUserSnapshot = await admin
        .firestore()
        .collection("loyalty_users")
        .doc(userId)
        .get();

      const userPath = loyaltyUserSnapshot.data()?.email || userId;
      const userSnapshot = await admin
        .firestore()
        .collection("users")
        .doc(userPath)
        .get();

      const deviceToken = userSnapshot.data()?.deviceToken;
      if (!deviceToken) return null;

      const message = buildNotifyMessageForLoyalty(
        transactionSnapshot.data(),
        deviceToken
      );
      if (message) {
        admin
          .messaging()
          .send(message)
          .then((response) => {
            console.log("Successfully sent message:", JSON.stringify(response));
          })
          .catch((error) => {
            console.log("Error sending message:", JSON.stringify(error));
          });
      }
    }
  }
);

// Realtime Database trigger for chat messages
exports.notifyOnRealtimeChatMessage = onValueCreated(
  "chat_messages/{messageId}",
  async (event) => {
    try {
      const messageData = event.data.val();
      const { messageId } = event.params;

      if (!messageData) {
        console.log("No message data found for messageId:", messageId);
        return;
      }

      console.log("New chat message created:", JSON.stringify(messageData));

      const {
        conversation_id,
        user_id: senderId,
        user_name: senderName,
        user_type: senderType,
        msg: messageText,
        vendor_id
      } = messageData;

      if (!conversation_id || !senderId) {
        console.log("Missing required fields: conversation_id or user_id");
        return;
      }

      // Get all users in the chat to find recipients
      console.log("Attempting to read chat_users from Realtime Database...");

      let chatUsers;
      try {
        const chatUsersSnapshot = await admin
          .database()
          .ref("chat_users")
          .once("value");

        console.log("Database snapshot exists:", chatUsersSnapshot.exists());

        chatUsers = chatUsersSnapshot.val();
        console.log("Raw chat users data:", chatUsers);

        if (!chatUsers) {
          console.log("No chat users found - chatUsers is null/undefined");

          // Try to read the entire database to debug
          console.log("Attempting to read root database to debug...");
          const rootSnapshot = await admin.database().ref("/").once("value");
          const rootData = rootSnapshot.val();
          console.log("Root database keys:", rootData ? Object.keys(rootData) : "No root data");

          return;
        }

        console.log(`Found ${Object.keys(chatUsers).length} chat users:`, Object.keys(chatUsers));
      } catch (error) {
        console.error("Error reading from Realtime Database:", error);
        return;
      }

      // Find recipients (users with same conversation_id or vendor_id, excluding sender)
      const recipients = [];

      console.log(`Looking for recipients for conversation_id: ${conversation_id}, vendor_id: ${vendor_id}, sender: ${senderId}`);

      for (const [userId, userData] of Object.entries(chatUsers)) {
        console.log(`Checking user ${userId}:`, {
          conversation_id: userData.conversation_id,
          vendor_id: userData.vendor_id,
          user_name: userData.user_name,
          hasPushToken: !!userData.pushToken
        });

        if (userId === senderId) {
          console.log(`Skipping sender: ${userId}`);
          continue; // Skip sender
        }

        // Check if user is part of the same conversation or vendor
        const isInSameConversation = userData.conversation_id === conversation_id;
        const isInSameVendor = vendor_id && userData.vendor_id === vendor_id;

        console.log(`User ${userId} - Same conversation: ${isInSameConversation}, Same vendor: ${isInSameVendor}`);

        if (isInSameConversation || isInSameVendor) {
          if (userData.pushToken) {
            recipients.push({
              userId: userId,
              pushToken: userData.pushToken,
              userName: userData.user_name || "User",
              userType: userData.user_type || "visitor"
            });
            console.log(`✅ Found recipient: ${userData.user_name} (${userId}) with pushToken`);
          } else {
            console.log(`❌ User ${userData.user_name} (${userId}) has no pushToken`);
          }
        } else {
          console.log(`❌ User ${userData.user_name} (${userId}) not in same conversation or vendor`);
        }
      }

      if (recipients.length === 0) {
        console.log("No recipients with push tokens found");
        return;
      }

      // Build notification messages for each recipient
      const notificationMessages = recipients.map((recipient) => {
        const notificationTitle = senderName
          ? `New message from ${senderName}`
          : "You have a new message";

        const notificationBody = messageText || "New message received";

        return {
          token: recipient.pushToken,
          notification: {
            title: notificationTitle,
            body: notificationBody,
          },
          data: {
            type: "chat_message",
            conversation_id: conversation_id,
            sender_id: senderId,
            sender_name: senderName || "",
            message_id: messageId,
            vendor_id: vendor_id ? vendor_id.toString() : ""
          },
          android: {
            priority: "high",
            notification: {
              sound: "default",
              channelId: "chat_messages"
            },
          },
          apns: {
            headers: {
              "apns-priority": "10",
            },
            payload: {
              aps: {
                sound: "default",
                badge: 1,
                category: "CHAT_MESSAGE"
              },
            },
          },
        };
      });

      console.log(`Sending ${notificationMessages.length} notifications`);

      // Send notifications
      const response = await admin
        .messaging()
        .sendEach(notificationMessages);

      console.log("Notification results:", {
        successCount: response.successCount,
        failureCount: response.failureCount,
        responses: response.responses.map((resp, index) => ({
          recipient: recipients[index].userName,
          success: resp.success,
          error: resp.error?.message || null
        }))
      });

      // Log any failures
      response.responses.forEach((resp, index) => {
        if (!resp.success) {
          console.error(`Failed to send notification to ${recipients[index].userName}:`, resp.error);
        }
      });

    } catch (error) {
      console.error("Error in notifyOnRealtimeChatMessage:", error);
    }
  }
);
